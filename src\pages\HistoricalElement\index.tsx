import React from 'react';
import { Card, Typography, Tag, Button } from 'antd';
import { EnvironmentOutlined, HistoryOutlined } from '@ant-design/icons';
import PublicLayout from '@/components/PublicLayout';
import { historicalElementData, regionDict, typeDict } from '@/services/mockData';
import { history } from '@umijs/max';

const { Title, Paragraph } = Typography;

const HistoricalElementPage: React.FC = () => {
  const handleDetailClick = (id: number) => {
    history.push(`/detail/historicalElement/${id}`);
  };

  const getRegionName = (regionId: number) => {
    const region = regionDict.find(r => r.id === regionId);
    return region?.region_name || '未知区域';
  };

  const getTypeName = (typeId: number) => {
    const type = typeDict.find(t => t.id === typeId);
    return type?.type_name || '未知类型';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}年`;
  };

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          关中地区历史要素
        </Title>
        
        <Paragraph style={{ textAlign: 'center', fontSize: 16, marginBottom: 32 }}>
          历史要素见证了关中地区悠久的文明历程，承载着丰富的文化内涵
        </Paragraph>

        <div className="list-container">
          {historicalElementData.map((element) => (
            <Card
              key={element.id}
              className="list-item"
              hoverable
              onClick={() => handleDetailClick(element.id)}
              cover={
                <div style={{ 
                  height: 200, 
                  background: 'linear-gradient(45deg, #FFB74D, #FF8A65)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 48,
                  color: 'white'
                }}>
                  🏛️
                </div>
              }
            >
              <Card.Meta
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{element.name}</span>
                    <Tag color="orange">{element.code}</Tag>
                  </div>
                }
                description={
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      <EnvironmentOutlined style={{ marginRight: 4 }} />
                      {getRegionName(element.region_dict_id)}
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <HistoryOutlined style={{ marginRight: 4 }} />
                      {getTypeName(element.type_dict_id)} · {formatDate(element.construction_time)}
                    </div>
                    <div style={{ marginBottom: 8, fontSize: 12, color: '#999' }}>
                      {element.location_description}
                    </div>
                    <Paragraph 
                      ellipsis={{ rows: 2 }} 
                      style={{ marginBottom: 16, color: '#666' }}
                    >
                      {element.historical_records}
                    </Paragraph>
                    <Button type="primary" size="small">
                      查看详情
                    </Button>
                  </div>
                }
              />
            </Card>
          ))}
        </div>
      </div>
    </PublicLayout>
  );
};

export default HistoricalElementPage;
