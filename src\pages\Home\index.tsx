import React, { useEffect, useRef, useState } from 'react';
import { Card, Modal, Button, Image, Typography } from 'antd';
import AMapLoader from '@amap/amap-jsapi-loader';
import PublicLayout from '@/components/PublicLayout';
import { mapData } from '@/services/mockData';
import { history } from '@umijs/max';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<any>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    initMap();
  }, []);

  const initMap = async () => {
    try {
      // 由于没有高德地图key，这里使用模拟地图
      if (mapRef.current) {
        // 创建一个简单的地图占位符
        mapRef.current.innerHTML = `
          <div style="
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            position: relative;
          ">
            <div style="margin-bottom: 20px;">关中地区地图</div>
            <div style="font-size: 14px; opacity: 0.8;">点击下方标记点查看详情</div>
            <div style="position: absolute; bottom: 20px; display: flex; gap: 20px;">
              <div style="cursor: pointer; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 8px;" onclick="window.showMountainInfo()">
                ⛰️ 山塬 (${mapData.mountains.length})
              </div>
              <div style="cursor: pointer; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 8px;" onclick="window.showWaterInfo()">
                🌊 水系 (${mapData.waterSystems.length})
              </div>
              <div style="cursor: pointer; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 8px;" onclick="window.showHistoricalInfo()">
                🏛️ 历史要素 (${mapData.historicalElements.length})
              </div>
            </div>
          </div>
        `;

        // 添加全局函数供地图调用
        (window as any).showMountainInfo = () => {
          setSelectedItem(mapData.mountains[0]);
          setModalVisible(true);
        };
        (window as any).showWaterInfo = () => {
          setSelectedItem(mapData.waterSystems[0]);
          setModalVisible(true);
        };
        (window as any).showHistoricalInfo = () => {
          setSelectedItem(mapData.historicalElements[0]);
          setModalVisible(true);
        };
      }
    } catch (error) {
      console.error('地图加载失败:', error);
    }
  };



  const handleDetailClick = () => {
    if (selectedItem) {
      history.push(`/detail/${selectedItem.type}/${selectedItem.id}`);
    }
    setModalVisible(false);
  };

  return (
    <PublicLayout>
      <div className="content-card">
        <div style={{ padding: '24px 24px 0' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>
            关中地区智慧营建系统
          </Title>
          <Paragraph style={{ textAlign: 'center', fontSize: 16, marginBottom: 24 }}>
            探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
          </Paragraph>
        </div>

        <div className="map-container" ref={mapRef} />

        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Paragraph>
            点击地图上的标记点查看详细信息，或通过导航栏浏览不同类型的要素
          </Paragraph>
        </div>
      </div>

      <Modal
        title={selectedItem?.name}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            关闭
          </Button>,
          <Button key="detail" type="primary" onClick={handleDetailClick}>
            查看详情
          </Button>,
        ]}
        width={600}
      >
        {selectedItem && (
          <div>
            <Paragraph>{selectedItem.historical_records}</Paragraph>
            {selectedItem.photos && selectedItem.photos.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <Image.PreviewGroup>
                  {selectedItem.photos.slice(0, 2).map((photo: any) => (
                    <Image
                      key={photo.id}
                      width={200}
                      src={photo.url}
                      alt={photo.name}
                      style={{ marginRight: 8 }}
                    />
                  ))}
                </Image.PreviewGroup>
              </div>
            )}
          </div>
        )}
      </Modal>
    </PublicLayout>
  );
};

export default HomePage;
