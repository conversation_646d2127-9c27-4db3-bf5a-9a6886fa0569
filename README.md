# 智慧营建系统

基于 Umi 4 + Ant Design 5 构建的智慧营建系统，用于展示和管理关中地区的山塬、水系、历史要素等地理文化信息。

## 系统特性

### 公共网站功能
- **首页地图展示**：展示关中地区的地理要素分布（使用模拟地图）
- **山塬展示**：展示山塬信息，包括海拔、位置、历史记载等
- **水系展示**：展示水系信息，包括长度、流域、历史意义等
- **历史要素展示**：展示历史建筑、文物等要素信息
- **数字化统计**：提供图表统计分析功能
- **详情页面**：提供详细的要素信息展示

### 管理端功能
- **用户登录**：支持管理员和编辑员角色
- **数据管理**：山塬、水系、历史要素的增删改查
- **字典管理**：区域、类型、关系字典的维护
- **资源管理**：图片等资源文件的上传和管理
- **用户管理**：系统用户的管理
- **仪表盘**：数据统计和快捷操作

## 技术栈

- **前端框架**：Umi 4 + React 18
- **UI 组件库**：Ant Design 5
- **图表库**：ECharts + echarts-for-react
- **地图组件**：@amap/amap-jsapi-loader（当前使用模拟地图）
- **样式**：Less + CSS-in-JS
- **状态管理**：Umi 内置状态管理
- **路由**：Umi 路由系统
- **构建工具**：Umi 内置构建系统

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

## 功能说明

### 公共网站访问
- 访问 `http://localhost:8000` 进入首页
- 通过导航栏可以访问各个功能页面
- 点击右上角"管理端"按钮进入管理系统

### 管理端访问
- 访问 `http://localhost:8000/admin/login` 进入登录页
- 测试账号：
  - 管理员：用户名 `admin`，密码 `123456`
  - 编辑员：用户名 `editor`，密码 `123456`

### 数据说明
- 当前使用模拟数据，包含关中地区的山塬、水系、历史要素信息
- 图片使用 picsum.photos 提供的占位图片
- 地图功能使用模拟地图（实际项目中需要配置高德地图 API Key）

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── PublicHeader/   # 公共网站头部
│   └── PublicLayout/   # 公共网站布局
├── pages/              # 页面组件
│   ├── Home/           # 首页
│   ├── Mountain/       # 山塬页面
│   ├── WaterSystem/    # 水系页面
│   ├── HistoricalElement/ # 历史要素页面
│   ├── Digital/        # 数字化页面
│   ├── Detail/         # 详情页面
│   └── Admin/          # 管理端页面
├── services/           # 服务层
│   └── mockData.ts     # 模拟数据
├── global.less         # 全局样式
├── app.ts             # 应用配置
└── access.ts          # 权限配置
```

## 开发说明

### 添加新页面
1. 在 `src/pages/` 下创建页面组件
2. 在 `.umirc.ts` 中配置路由
3. 根据需要添加到导航菜单

### 添加新的数据类型
1. 在 `src/services/mockData.ts` 中添加数据结构
2. 创建对应的管理页面
3. 更新相关的统计和展示逻辑

## 后续开发计划

1. **接口集成**：替换模拟数据为实际的后端 API
2. **地图功能**：集成真实的地图服务和标记功能
3. **权限细化**：实现更细粒度的权限控制
4. **数据导入导出**：支持批量数据的导入导出
5. **移动端适配**：优化移动端显示效果

## 许可证

MIT License
