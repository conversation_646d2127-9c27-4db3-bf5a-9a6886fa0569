import React, { useState, useEffect } from 'react';
import { Typography, Descriptions, Image, Card, Button, Tag } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useParams, history } from '@umijs/max';
import PublicLayout from '@/components/PublicLayout';
import { 
  mountainData, 
  waterSystemData, 
  historicalElementData, 
  photoData, 
  regionDict, 
  typeDict 
} from '@/services/mockData';

const { Title, Paragraph } = Typography;

const DetailPage: React.FC = () => {
  const { type, id } = useParams();
  const [data, setData] = useState<any>(null);
  const [photos, setPhotos] = useState<any[]>([]);

  useEffect(() => {
    loadData();
  }, [type, id]);

  const loadData = () => {
    let itemData = null;
    let itemPhotos: any[] = [];

    switch (type) {
      case 'mountain':
        itemData = mountainData.find(item => item.id === parseInt(id!));
        itemPhotos = photoData.filter(photo => photo.mountain_id === parseInt(id!));
        break;
      case 'waterSystem':
        itemData = waterSystemData.find(item => item.id === parseInt(id!));
        itemPhotos = photoData.filter(photo => photo.water_system_id === parseInt(id!));
        break;
      case 'historicalElement':
        itemData = historicalElementData.find(item => item.id === parseInt(id!));
        itemPhotos = photoData.filter(photo => photo.historical_element_id === parseInt(id!));
        break;
    }

    setData(itemData);
    setPhotos(itemPhotos);
  };

  const getRegionName = (regionId: number) => {
    const region = regionDict.find(r => r.id === regionId);
    return region?.region_name || '未知区域';
  };

  const getTypeName = (typeId: number) => {
    const typeItem = typeDict.find(t => t.id === typeId);
    return typeItem?.type_name || '未知类型';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  };

  const renderBasicInfo = () => {
    if (!data) return null;

    const items: any[] = [
      {
        key: 'name',
        label: '名称',
        children: data.name,
      },
      {
        key: 'code',
        label: '编号',
        children: <Tag color="blue">{data.code}</Tag>,
      },
      {
        key: 'region',
        label: '所属区域',
        children: getRegionName(data.region_dict_id),
      },
    ];

    // 根据类型添加特定字段
    switch (type) {
      case 'mountain':
        items.push(
          {
            key: 'height',
            label: '海拔高度',
            children: `${data.height}米`,
          },
          {
            key: 'coordinates',
            label: '地理坐标',
            children: `${data.longitude}, ${data.latitude}`,
          }
        );
        break;
      case 'waterSystem':
        items.push(
          {
            key: 'length',
            label: '长度/面积',
            children: data.length_area,
          },
          {
            key: 'coordinates',
            label: '地理坐标',
            children: `${data.longitude}, ${data.latitude}`,
          }
        );
        break;
      case 'historicalElement':
        items.push(
          {
            key: 'type',
            label: '类型',
            children: getTypeName(data.type_dict_id),
          },
          {
            key: 'time',
            label: '建造时间',
            children: formatDate(data.construction_time),
          },
          {
            key: 'location',
            label: '位置描述',
            children: data.location_description,
          },
          {
            key: 'coordinates',
            label: '地理坐标',
            children: `${data.construction_longitude}, ${data.construction_latitude}`,
          }
        );
        break;
    }

    items.push({
      key: 'records',
      label: '历史记载',
      children: data.historical_records,
    });

    return <Descriptions title="基本信息" bordered items={items} column={1} />;
  };

  if (!data) {
    return (
      <PublicLayout>
        <div className="content-card" style={{ padding: '24px', textAlign: 'center' }}>
          <Title level={3}>数据未找到</Title>
          <Button type="primary" icon={<ArrowLeftOutlined />} onClick={() => window.history.back()}>
            返回
          </Button>
        </div>
      </PublicLayout>
    );
  }

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <div style={{ marginBottom: 24 }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => window.history.back()}
            style={{ marginBottom: 16 }}
          >
            返回
          </Button>
          <Title level={2}>{data.name}</Title>
        </div>

        <div className="detail-layout">
          {/* 基础信息 */}
          <div className="detail-info">
            {renderBasicInfo()}
          </div>

          {/* 图片展示 */}
          <div className="detail-images">
            <Title level={4} style={{ marginBottom: 16 }}>相关图片</Title>
            {photos.length > 0 ? (
              <Image.PreviewGroup>
                {photos.map((photo) => (
                  <Image
                    key={photo.id}
                    width="100%"
                    src={photo.url}
                    alt={photo.name}
                    style={{ marginBottom: 8 }}
                  />
                ))}
              </Image.PreviewGroup>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                暂无相关图片
              </div>
            )}
          </div>

          {/* 关联信息 */}
          <div className="detail-relations">
            <Title level={4} style={{ marginBottom: 16 }}>关联信息</Title>
            <Card size="small" style={{ marginBottom: 8 }}>
              <div>选址关联</div>
              <div style={{ color: '#666', fontSize: 12 }}>
                与周边地理要素的空间关系
              </div>
            </Card>
            <Card size="small" style={{ marginBottom: 8 }}>
              <div>历史沿革</div>
              <div style={{ color: '#666', fontSize: 12 }}>
                历史发展脉络和文化传承
              </div>
            </Card>
            <Card size="small">
              <div>文化价值</div>
              <div style={{ color: '#666', fontSize: 12 }}>
                承载的文化内涵和价值意义
              </div>
            </Card>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default DetailPage;
