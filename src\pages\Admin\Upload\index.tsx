import React, { useState } from 'react';
import { 
  Upload, 
  Button, 
  Table, 
  Image, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Popconfirm,
  Space,
  Typography,
  Card 
} from 'antd';
import { 
  UploadOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  EyeOutlined,
  InboxOutlined 
} from '@ant-design/icons';
import { photoData, mountainData, waterSystemData, historicalElementData } from '@/services/mockData';

const { Title } = Typography;
const { Dragger } = Upload;

const AdminUpload: React.FC = () => {
  const [data, setData] = useState(photoData);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [form] = Form.useForm();

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '预览',
      dataIndex: 'url',
      key: 'preview',
      width: 100,
      render: (url: string) => (
        <Image
          width={60}
          height={40}
          src={url}
          style={{ objectFit: 'cover' }}
          preview={{
            mask: <EyeOutlined />,
          }}
        />
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '文件路径',
      dataIndex: 'url',
      key: 'url',
    },
    {
      title: '关联对象',
      key: 'related',
      render: (_: any, record: any) => {
        if (record.mountain_id) {
          const mountain = mountainData.find(m => m.id === record.mountain_id);
          return `山塬: ${mountain?.name || '未知'}`;
        }
        if (record.water_system_id) {
          const waterSystem = waterSystemData.find(w => w.id === record.water_system_id);
          return `水系: ${waterSystem?.name || '未知'}`;
        }
        if (record.historical_element_id) {
          const element = historicalElementData.find(h => h.id === record.historical_element_id);
          return `历史要素: ${element?.name || '未知'}`;
        }
        return '无关联';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个文件吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleEdit = (record: any) => {
    setEditingItem(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setData(data.filter(item => item.id !== id));
    message.success('删除成功！');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingItem) {
        // 编辑
        setData(data.map(item => 
          item.id === editingItem.id ? { ...item, ...values } : item
        ));
        message.success('编辑成功！');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const uploadProps = {
    name: 'file',
    multiple: true,
    action: '/api/upload', // 这里应该是实际的上传接口
    onChange(info: any) {
      const { status } = info.file;
      if (status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        message.success(`${info.file.name} 文件上传成功`);
        // 这里应该添加到数据列表中
        const newFile = {
          id: Date.now(),
          name: info.file.name,
          url: `/uploads/${info.file.name}`,
          mountain_id: null,
          water_system_id: null,
          historical_element_id: null,
        };
        setData([...data, newFile]);
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        资源管理
      </Title>

      {/* 上传区域 */}
      <Card title="文件上传" style={{ marginBottom: 24 }}>
        <Dragger {...uploadProps} style={{ padding: '20px' }}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传。支持图片格式：JPG、PNG、GIF等
          </p>
        </Dragger>
      </Card>

      {/* 文件列表 */}
      <Card title="文件列表">
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个文件`,
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑文件信息"
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="文件名称"
            rules={[{ required: true, message: '请输入文件名称' }]}
          >
            <Input placeholder="请输入文件名称" />
          </Form.Item>

          <Form.Item name="mountain_id" label="关联山塬">
            <Select placeholder="请选择关联的山塬" allowClear>
              {mountainData.map(mountain => (
                <Select.Option key={mountain.id} value={mountain.id}>
                  {mountain.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="water_system_id" label="关联水系">
            <Select placeholder="请选择关联的水系" allowClear>
              {waterSystemData.map(waterSystem => (
                <Select.Option key={waterSystem.id} value={waterSystem.id}>
                  {waterSystem.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="historical_element_id" label="关联历史要素">
            <Select placeholder="请选择关联的历史要素" allowClear>
              {historicalElementData.map(element => (
                <Select.Option key={element.id} value={element.id}>
                  {element.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUpload;
