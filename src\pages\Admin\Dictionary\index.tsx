import React, { useState } from 'react';
import { 
  Tabs, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  message, 
  Popconfirm,
  Space,
  Typography 
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { regionDict, typeDict, relationshipDict } from '@/services/mockData';

const { Title } = Typography;
const { TextArea } = Input;

const AdminDictionary: React.FC = () => {
  const [regionData, setRegionData] = useState(regionDict);
  const [typeData, setTypeData] = useState(typeDict);
  const [relationData, setRelationData] = useState(relationshipDict);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [currentTab, setCurrentTab] = useState('region');
  const [form] = Form.useForm();

  const regionColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '区域编码', dataIndex: 'region_code', key: 'region_code' },
    { title: '区域名称', dataIndex: 'region_name', key: 'region_name' },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status', 
      width: 80,
      render: (status: number) => status === 1 ? '启用' : '禁用'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'region')}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'region')}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const typeColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '类型编码', dataIndex: 'type_code', key: 'type_code' },
    { title: '类型名称', dataIndex: 'type_name', key: 'type_name' },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status', 
      width: 80,
      render: (status: number) => status === 1 ? '启用' : '禁用'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'type')}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'type')}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const relationColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '关系编码', dataIndex: 'relation_code', key: 'relation_code' },
    { title: '关系名称', dataIndex: 'relation_name', key: 'relation_name' },
    { title: '排序', dataIndex: 'sort', key: 'sort', width: 80 },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status', 
      width: 80,
      render: (status: number) => status === 1 ? '启用' : '禁用'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'relation')}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id, 'relation')}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = (type: string) => {
    setEditingItem(null);
    setCurrentTab(type);
    form.resetFields();
    form.setFieldsValue({ status: 1, parent_id: 0, sort: 1 });
    setModalVisible(true);
  };

  const handleEdit = (record: any, type: string) => {
    setEditingItem(record);
    setCurrentTab(type);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number, type: string) => {
    switch (type) {
      case 'region':
        setRegionData(regionData.filter(item => item.id !== id));
        break;
      case 'type':
        setTypeData(typeData.filter(item => item.id !== id));
        break;
      case 'relation':
        setRelationData(relationData.filter(item => item.id !== id));
        break;
    }
    message.success('删除成功！');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingItem) {
        // 编辑
        switch (currentTab) {
          case 'region':
            setRegionData(regionData.map(item => 
              item.id === editingItem.id ? { ...item, ...values } : item
            ));
            break;
          case 'type':
            setTypeData(typeData.map(item => 
              item.id === editingItem.id ? { ...item, ...values } : item
            ));
            break;
          case 'relation':
            setRelationData(relationData.map(item => 
              item.id === editingItem.id ? { ...item, ...values } : item
            ));
            break;
        }
        message.success('编辑成功！');
      } else {
        // 新增
        const newId = Date.now(); // 简单的ID生成
        switch (currentTab) {
          case 'region':
            setRegionData([...regionData, { id: newId, ...values }]);
            break;
          case 'type':
            setTypeData([...typeData, { id: newId, ...values }]);
            break;
          case 'relation':
            setRelationData([...relationData, { id: newId, ...values }]);
            break;
        }
        message.success('添加成功！');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const getModalTitle = () => {
    const action = editingItem ? '编辑' : '添加';
    switch (currentTab) {
      case 'region': return `${action}区域字典`;
      case 'type': return `${action}类型字典`;
      case 'relation': return `${action}关系字典`;
      default: return `${action}字典`;
    }
  };

  const renderFormFields = () => {
    switch (currentTab) {
      case 'region':
        return (
          <>
            <Form.Item name="region_code" label="区域编码" rules={[{ required: true }]}>
              <Input placeholder="请输入区域编码" />
            </Form.Item>
            <Form.Item name="region_name" label="区域名称" rules={[{ required: true }]}>
              <Input placeholder="请输入区域名称" />
            </Form.Item>
            <Form.Item name="region_desc" label="区域描述">
              <TextArea rows={3} placeholder="请输入区域描述" />
            </Form.Item>
          </>
        );
      case 'type':
        return (
          <>
            <Form.Item name="type_code" label="类型编码" rules={[{ required: true }]}>
              <Input placeholder="请输入类型编码" />
            </Form.Item>
            <Form.Item name="type_name" label="类型名称" rules={[{ required: true }]}>
              <Input placeholder="请输入类型名称" />
            </Form.Item>
            <Form.Item name="type_desc" label="类型描述">
              <TextArea rows={3} placeholder="请输入类型描述" />
            </Form.Item>
          </>
        );
      case 'relation':
        return (
          <>
            <Form.Item name="relation_code" label="关系编码" rules={[{ required: true }]}>
              <Input placeholder="请输入关系编码" />
            </Form.Item>
            <Form.Item name="relation_name" label="关系名称" rules={[{ required: true }]}>
              <Input placeholder="请输入关系名称" />
            </Form.Item>
            <Form.Item name="relation_desc" label="关系描述">
              <TextArea rows={3} placeholder="请输入关系描述" />
            </Form.Item>
          </>
        );
    }
  };

  const tabItems = [
    {
      key: 'region',
      label: '区域字典',
      children: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAdd('region')}>
              添加区域
            </Button>
          </div>
          <Table
            columns={regionColumns}
            dataSource={regionData}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </div>
      ),
    },
    {
      key: 'type',
      label: '类型字典',
      children: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAdd('type')}>
              添加类型
            </Button>
          </div>
          <Table
            columns={typeColumns}
            dataSource={typeData}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </div>
      ),
    },
    {
      key: 'relation',
      label: '关系字典',
      children: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAdd('relation')}>
              添加关系
            </Button>
          </div>
          <Table
            columns={relationColumns}
            dataSource={relationData}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        字典管理
      </Title>

      <Tabs items={tabItems} />

      <Modal
        title={getModalTitle()}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          {renderFormFields()}
          <Form.Item name="parent_id" label="父级ID">
            <InputNumber style={{ width: '100%' }} placeholder="0表示顶级" min={0} />
          </Form.Item>
          <Form.Item name="sort" label="排序">
            <InputNumber style={{ width: '100%' }} placeholder="请输入排序号" min={1} />
          </Form.Item>
          <Form.Item name="status" label="状态" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminDictionary;
